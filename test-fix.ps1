# 项目删除功能修复测试脚本
# 用于验证修复后的项目删除功能

Write-Host "🚀 开始项目删除功能修复测试" -ForegroundColor Green

# 检查Docker是否运行
Write-Host "🔍 检查Docker状态..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Docker正在运行" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 停止现有容器
Write-Host "🛑 停止现有容器..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml down

# 清理旧的容器和网络
Write-Host "🧹 清理旧的容器和网络..." -ForegroundColor Yellow
docker system prune -f

# 构建并启动服务
Write-Host "🏗️ 构建并启动服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.windows.yml up -d --build

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
$services = docker-compose -f docker-compose.windows.yml ps --format json | ConvertFrom-Json

$healthyServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    $serviceName = $service.Service
    $state = $service.State
    
    if ($state -eq "running") {
        Write-Host "✅ $serviceName: 运行中" -ForegroundColor Green
        $healthyServices++
    } else {
        Write-Host "❌ $serviceName: $state" -ForegroundColor Red
    }
}

Write-Host "📊 服务状态: $healthyServices/$totalServices 个服务正常运行" -ForegroundColor Cyan

# 检查关键服务
$criticalServices = @("mysql", "redis", "api-gateway", "project-service")
$allCriticalHealthy = $true

foreach ($service in $criticalServices) {
    $serviceStatus = docker-compose -f docker-compose.windows.yml ps $service --format json | ConvertFrom-Json
    if ($serviceStatus.State -ne "running") {
        Write-Host "❌ 关键服务 $service 未运行" -ForegroundColor Red
        $allCriticalHealthy = $false
    }
}

if ($allCriticalHealthy) {
    Write-Host "✅ 所有关键服务正常运行" -ForegroundColor Green
} else {
    Write-Host "❌ 部分关键服务未运行，请检查日志" -ForegroundColor Red
    
    # 显示失败服务的日志
    foreach ($service in $criticalServices) {
        $serviceStatus = docker-compose -f docker-compose.windows.yml ps $service --format json | ConvertFrom-Json
        if ($serviceStatus.State -ne "running") {
            Write-Host "📋 $service 服务日志:" -ForegroundColor Yellow
            docker-compose -f docker-compose.windows.yml logs --tail=20 $service
        }
    }
}

# 等待数据库初始化
Write-Host "⏳ 等待数据库初始化..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# 测试API连接
Write-Host "🔗 测试API连接..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3000/api/health" -Method Get -TimeoutSec 10
    Write-Host "✅ API网关连接成功" -ForegroundColor Green
} catch {
    Write-Host "❌ API网关连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试项目服务连接
Write-Host "🔗 测试项目服务连接..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:4002/health" -Method Get -TimeoutSec 10
    Write-Host "✅ 项目服务连接成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 项目服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 运行Node.js测试脚本
if (Test-Path "test-project-delete-fix.js") {
    Write-Host "🧪 运行项目删除功能测试..." -ForegroundColor Yellow
    
    # 检查Node.js是否安装
    try {
        $nodeVersion = node --version
        Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
        
        # 安装依赖
        if (-not (Test-Path "node_modules/axios")) {
            Write-Host "📦 安装测试依赖..." -ForegroundColor Yellow
            npm install axios
        }
        
        # 运行测试
        node test-project-delete-fix.js
    } catch {
        Write-Host "❌ Node.js测试失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 请确保已安装Node.js" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ 测试脚本 test-project-delete-fix.js 不存在" -ForegroundColor Yellow
}

Write-Host "🏁 测试完成" -ForegroundColor Green
Write-Host "📋 如需查看详细日志，请运行:" -ForegroundColor Cyan
Write-Host "   docker-compose -f docker-compose.windows.yml logs [service-name]" -ForegroundColor Gray

# 显示有用的URL
Write-Host "🔗 有用的链接:" -ForegroundColor Cyan
Write-Host "   API文档: http://localhost:3000/api/docs" -ForegroundColor Gray
Write-Host "   前端应用: http://localhost:80" -ForegroundColor Gray
Write-Host "   MinIO控制台: http://localhost:9001" -ForegroundColor Gray

Write-Host "💡 提示: 如果遇到问题，请检查 .env 文件配置和服务日志" -ForegroundColor Yellow
