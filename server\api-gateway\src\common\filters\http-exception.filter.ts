/**
 * HTTP异常过滤器
 */
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';
    let error = 'Internal Server Error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const errorResponse = exception.getResponse();

      if (typeof errorResponse === 'string') {
        message = errorResponse;
      } else if (typeof errorResponse === 'object' && errorResponse !== null) {
        message = (errorResponse as any).message || message;
        error = (errorResponse as any).error || exception.name;

        // 处理验证错误
        if (Array.isArray((errorResponse as any).message)) {
          message = (errorResponse as any).message.join(', ');
        }
      }
    } else {
      // 处理非HTTP异常
      message = exception.message || '服务器内部错误';
      error = exception.name || 'Internal Server Error';
    }

    // 根据状态码提供更友好的错误信息
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        if (message === 'Bad Request') {
          message = '请求参数错误';
        }
        break;
      case HttpStatus.UNAUTHORIZED:
        if (message === 'Unauthorized') {
          message = '认证失败，请重新登录';
        }
        break;
      case HttpStatus.FORBIDDEN:
        if (message === 'Forbidden') {
          message = '权限不足，无法执行此操作';
        }
        break;
      case HttpStatus.NOT_FOUND:
        if (message === 'Not Found') {
          message = '请求的资源不存在';
        }
        break;
      case HttpStatus.INTERNAL_SERVER_ERROR:
        message = '服务器内部错误，请稍后重试';
        break;
    }

    // 记录错误日志
    const logMessage = `${request.method} ${request.url} ${status} - ${message}`;
    if (status >= 500) {
      this.logger.error(logMessage, exception.stack);
    } else if (status >= 400) {
      this.logger.warn(logMessage);
    }

    // 返回统一的错误响应格式
    const errorResponse = {
      success: false,
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      error,
      message,
      // 开发环境下返回详细错误信息
      ...(process.env.NODE_ENV === 'development' && {
        details: exception.stack
      })
    };

    response.status(status).json(errorResponse);
  }
}
