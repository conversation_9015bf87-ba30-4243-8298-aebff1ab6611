/**
 * 项目删除功能修复验证脚本
 * 用于测试项目删除API的修复效果
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123456'
};

let authToken = '';

/**
 * 登录获取token
 */
async function login() {
  try {
    console.log('🔐 正在登录...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    });

    if (response.data && response.data.access_token) {
      authToken = response.data.access_token;
      console.log('✅ 登录成功');
      return true;
    } else {
      console.log('❌ 登录失败：未获取到token');
      return false;
    }
  } catch (error) {
    console.log('❌ 登录失败:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 创建测试项目
 */
async function createTestProject() {
  try {
    console.log('📁 正在创建测试项目...');
    const response = await axios.post(`${API_BASE_URL}/projects`, {
      name: `测试项目_${Date.now()}`,
      description: '用于测试删除功能的项目',
      isPublic: false
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.id) {
      console.log('✅ 测试项目创建成功:', response.data.name);
      return response.data;
    } else {
      console.log('❌ 创建项目失败：响应格式异常');
      return null;
    }
  } catch (error) {
    console.log('❌ 创建项目失败:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试项目删除
 */
async function testProjectDelete(projectId) {
  try {
    console.log('🗑️  正在测试项目删除...');
    const response = await axios.delete(`${API_BASE_URL}/projects/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200 || response.status === 204) {
      console.log('✅ 项目删除成功');
      console.log('响应数据:', response.data);
      return true;
    } else {
      console.log('❌ 项目删除失败：状态码异常', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 项目删除失败:');
    console.log('  状态码:', error.response?.status);
    console.log('  错误信息:', error.response?.data?.message || error.message);
    console.log('  完整响应:', error.response?.data);
    return false;
  }
}

/**
 * 验证项目是否已删除
 */
async function verifyProjectDeleted(projectId) {
  try {
    console.log('🔍 正在验证项目是否已删除...');
    const response = await axios.get(`${API_BASE_URL}/projects/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    console.log('❌ 项目仍然存在，删除可能失败');
    return false;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ 项目已成功删除（404 Not Found）');
      return true;
    } else {
      console.log('⚠️  验证删除状态时出现其他错误:', error.response?.status, error.response?.data?.message);
      return false;
    }
  }
}

/**
 * 检查API健康状态
 */
async function checkApiHealth() {
  try {
    console.log('🏥 正在检查API健康状态...');
    const response = await axios.get(`${API_BASE_URL}/health`);
    
    if (response.status === 200) {
      console.log('✅ API服务正常');
      return true;
    } else {
      console.log('❌ API服务异常');
      return false;
    }
  } catch (error) {
    console.log('❌ API服务不可用:', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('🚀 开始项目删除功能修复验证\n');

  // 1. 检查API健康状态
  const isHealthy = await checkApiHealth();
  if (!isHealthy) {
    console.log('\n❌ 测试终止：API服务不可用');
    return;
  }

  // 2. 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 测试终止：登录失败');
    return;
  }

  // 3. 创建测试项目
  const project = await createTestProject();
  if (!project) {
    console.log('\n❌ 测试终止：创建项目失败');
    return;
  }

  // 4. 测试删除项目
  const deleteSuccess = await testProjectDelete(project.id);
  
  // 5. 验证删除结果
  if (deleteSuccess) {
    await verifyProjectDeleted(project.id);
  }

  console.log('\n🏁 测试完成');
}

// 运行测试
runTest().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
